import re
from typing import List, Tuple
from collections import Counter
import math


class CVMatcher:
    def __init__(self):
        # Common stop words to ignore
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'is', 'are', 'was', 'were', 'be', 'been',
            'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
        }

    def preprocess_text(self, text: str) -> List[str]:
        """Preprocess text by cleaning and tokenizing"""
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters and keep only alphanumeric and spaces
        text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
        
        # Split into words
        words = text.split()
        
        # Remove stop words and short words
        words = [word for word in words if word not in self.stop_words and len(word) > 2]
        
        return words

    def calculate_tf_idf_similarity(self, job_description: str, cv_content: str) -> float:
        """Calculate TF-IDF based similarity with multilingual improvements"""
        # Preprocess texts
        job_words = self.preprocess_text(job_description)
        cv_words = self.preprocess_text(cv_content)

        # Add synonym expansion for better matching
        synonyms = {
            'developer': ['entwickler', 'programmer', 'programmierer'],
            'experience': ['erfahrung', 'expertise'],
            'software': ['software', 'anwendung'],
            'project': ['projekt', 'vorhaben'],
            'team': ['team', 'gruppe'],
            'management': ['management', 'führung', 'leitung'],
            'database': ['datenbank', 'db'],
            'web': ['web', 'internet', 'online'],
            'programming': ['programmierung', 'entwicklung'],
            'skills': ['fähigkeiten', 'kenntnisse']
        }

        # Expand vocabulary with synonyms
        enhanced_job_words = job_words.copy()
        enhanced_cv_words = cv_words.copy()

        for word in job_words:
            if word in synonyms:
                enhanced_job_words.extend(synonyms[word])

        for word in cv_words:
            if word in synonyms:
                enhanced_cv_words.extend(synonyms[word])

        # Create vocabulary from enhanced word lists
        all_words = set(enhanced_job_words + enhanced_cv_words)

        if not all_words:
            return 0.1  # Give a small base score

        # Calculate term frequencies
        job_tf = Counter(enhanced_job_words)
        cv_tf = Counter(enhanced_cv_words)

        # Calculate similarity using cosine similarity
        dot_product = 0
        job_magnitude = 0
        cv_magnitude = 0

        for word in all_words:
            job_freq = job_tf.get(word, 0)
            cv_freq = cv_tf.get(word, 0)

            dot_product += job_freq * cv_freq
            job_magnitude += job_freq ** 2
            cv_magnitude += cv_freq ** 2

        if job_magnitude == 0 or cv_magnitude == 0:
            return 0.1  # Give a small base score instead of 0

        similarity = dot_product / (math.sqrt(job_magnitude) * math.sqrt(cv_magnitude))

        # Scale the similarity to make it more meaningful
        scaled_similarity = similarity * 3.0  # Boost the score

        return min(1.0, scaled_similarity)  # Cap at 1.0

    def calculate_keyword_match(self, job_description: str, cv_content: str) -> float:
        """Calculate keyword-based matching score with improved algorithm"""
        job_words = set(self.preprocess_text(job_description))
        cv_words = set(self.preprocess_text(cv_content))

        if not job_words:
            return 0.0

        # Calculate intersection
        intersection = job_words.intersection(cv_words)

        # Use a more favorable scoring method than Jaccard
        # Focus on how many job requirements are met rather than overall similarity
        job_coverage = len(intersection) / len(job_words) if job_words else 0

        # Add bonus for important keywords
        important_keywords = {
            'developer', 'engineer', 'programmer', 'analyst', 'manager',
            'entwickler', 'ingenieur', 'programmierer', 'analyst', 'manager',
            'experience', 'erfahrung', 'skills', 'fähigkeiten',
            'project', 'projekt', 'team', 'software'
        }

        important_matches = len(intersection.intersection(important_keywords))
        importance_bonus = important_matches * 0.1  # 10% bonus per important keyword

        # Combine coverage with importance bonus
        final_score = job_coverage + importance_bonus

        return min(1.0, final_score)  # Cap at 1.0

    def calculate_skill_match(self, job_description: str, cv_content: str) -> float:
        """Calculate skill-specific matching score with multilingual support"""
        # Enhanced skill keywords with German translations
        technical_skills = [
            # Programming languages
            'python', 'java', 'javascript', 'react', 'angular', 'vue', 'node',
            'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'kotlin', 'swift',

            # Databases
            'sql', 'mysql', 'postgresql', 'mongodb', 'oracle', 'sqlite',
            'datenbank', 'datenbanken',  # German: database(s)

            # DevOps & Cloud
            'docker', 'kubernetes', 'aws', 'azure', 'gcp', 'jenkins', 'git',
            'linux', 'windows', 'unix', 'bash', 'powershell',

            # Web Development
            'html', 'css', 'bootstrap', 'jquery', 'ajax', 'rest', 'api',
            'webentwicklung', 'webanwendung',  # German: web development, web application

            # Methodologies
            'agile', 'scrum', 'kanban', 'devops', 'ci/cd',

            # German technical terms
            'softwareentwicklung', 'programmierung', 'entwicklung',
            'systemadministration', 'netzwerk', 'sicherheit'
        ]

        soft_skills = [
            # English soft skills
            'leadership', 'communication', 'teamwork', 'management', 'analysis',
            'problem solving', 'critical thinking', 'project management',
            'collaboration', 'organization', 'planning', 'coordination',

            # German soft skills
            'führung', 'kommunikation', 'teamarbeit', 'teamwork', 'management',
            'projektmanagement', 'organisation', 'planung', 'koordination',
            'problemlösung', 'analytisches denken', 'zusammenarbeit',
            'verantwortung', 'selbstständigkeit', 'zuverlässigkeit'
        ]

        # Experience-related terms
        experience_terms = [
            'experience', 'years', 'jahr', 'jahre', 'erfahrung', 'berufserfahrung',
            'expertise', 'skilled', 'proficient', 'advanced', 'senior', 'junior'
        ]

        all_skills = technical_skills + soft_skills + experience_terms

        job_lower = job_description.lower()
        cv_lower = cv_content.lower()

        # Find skills in job description and CV
        job_skills = []
        cv_skills = []

        for skill in all_skills:
            if skill in job_lower:
                job_skills.append(skill)
            if skill in cv_lower:
                cv_skills.append(skill)

        # If no skills found in job description, use a more lenient approach
        if not job_skills:
            # Look for any technical terms that might indicate requirements
            general_terms = ['developer', 'engineer', 'programmer', 'analyst', 'manager']
            job_skills = [term for term in general_terms if term in job_lower]

        if not job_skills:
            return 0.5  # Give a base score if no specific skills are mentioned

        # Calculate skill overlap
        matched_skills = set(job_skills).intersection(set(cv_skills))
        skill_match_ratio = len(matched_skills) / len(set(job_skills))

        # Bonus for having more skills than required
        cv_skill_count = len(set(cv_skills))
        job_skill_count = len(set(job_skills))

        if cv_skill_count > job_skill_count:
            bonus = min(0.2, (cv_skill_count - job_skill_count) * 0.05)
            skill_match_ratio += bonus

        return min(1.0, skill_match_ratio)  # Cap at 1.0

    def calculate_match_score(self, cv_path: str, job_description: str) -> float:
        """Calculate overall match score between CV and job description"""
        try:
            # Extract text from CV
            from cv_extractor import CVDataExtractor
            extractor = CVDataExtractor()
            cv_content = extractor.extract_text_from_file(cv_path)
            
            if not cv_content:
                return 0.0
            
            # Calculate different similarity metrics
            tf_idf_score = self.calculate_tf_idf_similarity(job_description, cv_content)
            keyword_score = self.calculate_keyword_match(job_description, cv_content)
            skill_score = self.calculate_skill_match(job_description, cv_content)
            
            # Improved weighted combination of scores
            # Skills: 50%, Keywords: 30%, TF-IDF: 20% (prioritize skills and keywords)
            overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
            
            # Convert to percentage
            return overall_score * 100
            
        except Exception as e:
            print(f"Error calculating match score: {e}")
            return 0.0

    def match(self, job_description: str, cv_contents: List[str]) -> List[Tuple[float, str]]:
        """Match multiple CVs against a job description and return sorted results"""
        results = []
        
        for i, cv_content in enumerate(cv_contents):
            # Calculate different similarity metrics
            tf_idf_score = self.calculate_tf_idf_similarity(job_description, cv_content)
            keyword_score = self.calculate_keyword_match(job_description, cv_content)
            skill_score = self.calculate_skill_match(job_description, cv_content)
            
            # Improved weighted combination of scores
            overall_score = (skill_score * 0.5) + (keyword_score * 0.3) + (tf_idf_score * 0.2)
            
            results.append((overall_score, cv_content))
        
        # Sort by score in descending order
        results.sort(key=lambda x: x[0], reverse=True)
        
        return results

    def get_match_explanation(self, job_description: str, cv_content: str) -> dict:
        """Get detailed explanation of match score"""
        tf_idf_score = self.calculate_tf_idf_similarity(job_description, cv_content)
        keyword_score = self.calculate_keyword_match(job_description, cv_content)
        skill_score = self.calculate_skill_match(job_description, cv_content)
        
        overall_score = (tf_idf_score * 0.4) + (keyword_score * 0.3) + (skill_score * 0.3)
        
        # Find common keywords
        job_words = set(self.preprocess_text(job_description))
        cv_words = set(self.preprocess_text(cv_content))
        common_keywords = job_words.intersection(cv_words)
        
        return {
            'overall_score': overall_score * 100,
            'tf_idf_score': tf_idf_score * 100,
            'keyword_score': keyword_score * 100,
            'skill_score': skill_score * 100,
            'common_keywords': list(common_keywords)[:10],  # Top 10 common keywords
            'explanation': f"Overall match: {overall_score*100:.1f}% "
                          f"(Content similarity: {tf_idf_score*100:.1f}%, "
                          f"Keyword match: {keyword_score*100:.1f}%, "
                          f"Skill match: {skill_score*100:.1f}%)"
        }
