"""
Listmonk Integration for BAUCH HR Management System
Professional open-source email marketing integration
"""

import requests
import json
import logging
from typing import List, Dict, Optional, Any
from datetime import datetime


class ListmonkEmailService:
    """
    Professional email service using Listmonk open-source email marketing platform
    """
    
    def __init__(self, base_url: str = "http://localhost:9000", username: str = "admin", password: str = "listmonk"):
        """
        Initialize Listmonk email service
        
        Args:
            base_url: Listmonk server URL
            username: Admin username
            password: Admin password
        """
        self.base_url = base_url.rstrip('/')
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.session.auth = (username, password)
        self.logger = self._setup_logger()
        
        # Test connection
        self._test_connection()
    
    def _setup_logger(self) -> logging.Logger:
        """Set up logging for the email service"""
        logger = logging.getLogger('listmonk_email_service')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _test_connection(self) -> bool:
        """Test connection to Listmonk server"""
        try:
            response = self.session.get(f"{self.base_url}/api/health")
            if response.status_code == 200:
                self.logger.info("✅ Connected to Listmonk server successfully")
                return True
            else:
                self.logger.warning(f"⚠️ Listmonk server responded with status {response.status_code}")
                return False
        except Exception as e:
            self.logger.error(f"❌ Failed to connect to Listmonk server: {e}")
            return False
    
    def create_list(self, name: str, description: str = "", list_type: str = "public") -> Optional[Dict]:
        """
        Create a new mailing list
        
        Args:
            name: List name
            description: List description
            list_type: List type (public, private)
        
        Returns:
            Created list data or None if failed
        """
        try:
            data = {
                "name": name,
                "description": description,
                "type": list_type,
                "optin": "single"  # single or double opt-in
            }
            
            response = self.session.post(f"{self.base_url}/api/lists", json=data)
            
            if response.status_code == 200:
                list_data = response.json()["data"]
                self.logger.info(f"✅ Created list: {name} (ID: {list_data['id']})")
                return list_data
            else:
                self.logger.error(f"❌ Failed to create list: {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error creating list: {e}")
            return None
    
    def get_or_create_list(self, name: str, description: str = "") -> Optional[Dict]:
        """Get existing list or create new one"""
        try:
            # Try to find existing list
            response = self.session.get(f"{self.base_url}/api/lists")
            if response.status_code == 200:
                lists = response.json()["data"]["results"]
                for lst in lists:
                    if lst["name"] == name:
                        self.logger.info(f"📋 Found existing list: {name} (ID: {lst['id']})")
                        return lst
            
            # Create new list if not found
            return self.create_list(name, description)
            
        except Exception as e:
            self.logger.error(f"❌ Error getting/creating list: {e}")
            return None
    
    def add_subscriber(self, email: str, name: str, list_ids: List[int], 
                      attributes: Optional[Dict] = None) -> bool:
        """
        Add subscriber to lists
        
        Args:
            email: Subscriber email
            name: Subscriber name
            list_ids: List of list IDs to subscribe to
            attributes: Additional subscriber attributes
        
        Returns:
            True if successful, False otherwise
        """
        try:
            data = {
                "email": email,
                "name": name,
                "status": "enabled",
                "lists": list_ids,
                "attribs": attributes or {}
            }
            
            response = self.session.post(f"{self.base_url}/api/subscribers", json=data)
            
            if response.status_code == 200:
                subscriber = response.json()["data"]
                self.logger.info(f"✅ Added subscriber: {email} (ID: {subscriber['id']})")
                return True
            else:
                self.logger.error(f"❌ Failed to add subscriber {email}: {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error adding subscriber: {e}")
            return False
    
    def create_campaign(self, name: str, subject: str, list_ids: List[int], 
                       content_html: str, content_text: str = "") -> Optional[Dict]:
        """
        Create email campaign
        
        Args:
            name: Campaign name
            subject: Email subject
            list_ids: Target list IDs
            content_html: HTML content
            content_text: Plain text content (optional)
        
        Returns:
            Campaign data or None if failed
        """
        try:
            data = {
                "name": name,
                "subject": subject,
                "lists": list_ids,
                "type": "regular",
                "content_type": "html",
                "body": content_html,
                "alt_body": content_text,
                "send_at": None,  # Send immediately
                "messenger": "email"
            }
            
            response = self.session.post(f"{self.base_url}/api/campaigns", json=data)
            
            if response.status_code == 200:
                campaign = response.json()["data"]
                self.logger.info(f"✅ Created campaign: {name} (ID: {campaign['id']})")
                return campaign
            else:
                self.logger.error(f"❌ Failed to create campaign: {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error creating campaign: {e}")
            return None
    
    def send_campaign(self, campaign_id: int) -> bool:
        """
        Send campaign
        
        Args:
            campaign_id: Campaign ID to send
        
        Returns:
            True if successful, False otherwise
        """
        try:
            response = self.session.put(f"{self.base_url}/api/campaigns/{campaign_id}/status", 
                                      json={"status": "running"})
            
            if response.status_code == 200:
                self.logger.info(f"✅ Campaign {campaign_id} sent successfully")
                return True
            else:
                self.logger.error(f"❌ Failed to send campaign {campaign_id}: {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error sending campaign: {e}")
            return False
    
    def send_bulk_emails(self, recipients: List[Dict], subject: str, template: str, 
                        job_title: str = "", async_send: bool = False) -> Dict:
        """
        Send bulk emails to recipients (HR-specific method)
        
        Args:
            recipients: List of recipient dictionaries with email, name, etc.
            subject: Email subject
            template: Email template with placeholders
            job_title: Job title for list naming
            async_send: Whether to send asynchronously
        
        Returns:
            Dictionary with success/failure counts
        """
        try:
            # Create or get list for this job
            list_name = f"HR - {job_title}" if job_title else "HR - General"
            mailing_list = self.get_or_create_list(
                name=list_name,
                description=f"Applicants for {job_title}" if job_title else "General HR communications"
            )
            
            if not mailing_list:
                return {"success": 0, "failed": len(recipients), "error": "Failed to create mailing list"}
            
            list_id = mailing_list["id"]
            
            # Add subscribers
            successful_subscribers = []
            failed_subscribers = []
            
            for recipient in recipients:
                email = recipient.get("email")
                name = recipient.get("name", "Candidate")
                
                if not email:
                    failed_subscribers.append(recipient)
                    continue
                
                # Add subscriber with HR-specific attributes
                attributes = {
                    "job_title": recipient.get("job_title", ""),
                    "status": recipient.get("status", ""),
                    "application_date": datetime.now().isoformat()
                }
                
                if self.add_subscriber(email, name, [list_id], attributes):
                    successful_subscribers.append(recipient)
                else:
                    failed_subscribers.append(recipient)
            
            if not successful_subscribers:
                return {"success": 0, "failed": len(recipients), "error": "No valid subscribers added"}
            
            # Format template with first recipient data for preview
            sample_recipient = successful_subscribers[0]
            formatted_content = template.format(**sample_recipient)
            
            # Create campaign
            campaign_name = f"HR Campaign - {job_title} - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            campaign = self.create_campaign(
                name=campaign_name,
                subject=subject,
                list_ids=[list_id],
                content_html=formatted_content,
                content_text=""
            )
            
            if not campaign:
                return {"success": 0, "failed": len(recipients), "error": "Failed to create campaign"}
            
            # Send campaign
            if self.send_campaign(campaign["id"]):
                return {
                    "success": len(successful_subscribers),
                    "failed": len(failed_subscribers),
                    "campaign_id": campaign["id"],
                    "list_id": list_id,
                    "recipients": successful_subscribers
                }
            else:
                return {"success": 0, "failed": len(recipients), "error": "Failed to send campaign"}
                
        except Exception as e:
            self.logger.error(f"❌ Error in bulk email sending: {e}")
            return {"success": 0, "failed": len(recipients), "error": str(e)}
    
    def get_campaign_stats(self, campaign_id: int) -> Optional[Dict]:
        """Get campaign statistics"""
        try:
            response = self.session.get(f"{self.base_url}/api/campaigns/{campaign_id}")
            if response.status_code == 200:
                return response.json()["data"]
            return None
        except Exception as e:
            self.logger.error(f"❌ Error getting campaign stats: {e}")
            return None
    
    def get_default_templates(self, language: str = 'en') -> Dict[str, str]:
        """
        Get default HR email templates
        
        Args:
            language: Template language ('en' or 'de')
        
        Returns:
            Dictionary of template names and content
        """
        if language == 'de':
            return {
                'application_received': '''
                <h2>Bewerbung eingegangen</h2>
                <p>Sehr geehrte(r) {name},</p>
                <p>vielen Dank für Ihre Bewerbung für die Position <strong>{job_title}</strong> in unserem Unternehmen.</p>
                <p>Wir haben Ihre Bewerbung erhalten und werden sie in Kürze prüfen. Sie werden von uns hören, sobald wir Ihre Unterlagen ausgewertet haben.</p>
                <p>Bei Fragen stehen wir Ihnen gerne zur Verfügung.</p>
                <p>Mit freundlichen Grüßen,<br>
                Ihr HR-Team<br>
                BAUCH Group</p>
                ''',
                'interview_invitation': '''
                <h2>Einladung zum Vorstellungsgespräch</h2>
                <p>Sehr geehrte(r) {name},</p>
                <p>wir freuen uns, Sie zu einem Vorstellungsgespräch für die Position <strong>{job_title}</strong> einzuladen.</p>
                <p><strong>Termin:</strong> {interview_date}<br>
                <strong>Uhrzeit:</strong> {interview_time}<br>
                <strong>Ort:</strong> {interview_location}</p>
                <p>Bitte bestätigen Sie Ihre Teilnahme durch eine Antwort auf diese E-Mail.</p>
                <p>Mit freundlichen Grüßen,<br>
                Ihr HR-Team<br>
                BAUCH Group</p>
                ''',
                'status_update': '''
                <h2>Aktualisierung des Bewerbungsstatus</h2>
                <p>Sehr geehrte(r) {name},</p>
                <p>wir möchten Sie über den aktuellen Status Ihrer Bewerbung für die Position <strong>{job_title}</strong> informieren.</p>
                <p><strong>Status:</strong> {status}</p>
                <p>{additional_info}</p>
                <p>Mit freundlichen Grüßen,<br>
                Ihr HR-Team<br>
                BAUCH Group</p>
                '''
            }
        else:
            return {
                'application_received': '''
                <h2>Application Received</h2>
                <p>Dear {name},</p>
                <p>Thank you for your application for the position <strong>{job_title}</strong> at our company.</p>
                <p>We have received your application and will review it shortly. You will hear from us once we have evaluated your documents.</p>
                <p>If you have any questions, please feel free to contact us.</p>
                <p>Best regards,<br>
                Your HR Team<br>
                BAUCH Group</p>
                ''',
                'interview_invitation': '''
                <h2>Interview Invitation</h2>
                <p>Dear {name},</p>
                <p>We are pleased to invite you for an interview for the position <strong>{job_title}</strong>.</p>
                <p><strong>Date:</strong> {interview_date}<br>
                <strong>Time:</strong> {interview_time}<br>
                <strong>Location:</strong> {interview_location}</p>
                <p>Please confirm your attendance by replying to this email.</p>
                <p>Best regards,<br>
                Your HR Team<br>
                BAUCH Group</p>
                ''',
                'status_update': '''
                <h2>Application Status Update</h2>
                <p>Dear {name},</p>
                <p>We would like to inform you about the current status of your application for the position <strong>{job_title}</strong>.</p>
                <p><strong>Status:</strong> {status}</p>
                <p>{additional_info}</p>
                <p>Best regards,<br>
                Your HR Team<br>
                BAUCH Group</p>
                '''
            }
